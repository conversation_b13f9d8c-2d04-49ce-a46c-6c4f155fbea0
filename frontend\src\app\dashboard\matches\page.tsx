import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";

export default function MatchesPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Matches</h1>
        <p className="text-muted-foreground">Find your perfect match based on psychology.</p>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i} className="overflow-hidden">
            <div className="h-48 bg-[#F2E7DB] flex items-center justify-center">
              <div className="w-24 h-24 rounded-full bg-[#D0544D]/20 flex items-center justify-center">
                <span className="text-[#D0544D] text-xl font-bold">
                  {String.fromCharCode(65 + i)}
                </span>
              </div>
            </div>
            <CardHeader>
              <CardTitle>Match #{i + 1}</CardTitle>
              <CardDescription>Compatibility: {85 - i * 5}%</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm">This is a placeholder for match information. In the real app, this would contain details about your potential match.</p>
              <div className="mt-4">
                <button className="px-4 py-2 bg-[#D0544D] text-white rounded-md hover:bg-[#D0544D]/90 transition-colors">
                  View Profile
                </button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
